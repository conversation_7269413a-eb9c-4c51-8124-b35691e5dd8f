import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diogeneschatbot/models/api_usage_record.dart';
import 'package:diogeneschatbot/models/usage_chart_data.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/usage_chart_widget.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_button.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class UsageHistoryPage extends StatefulWidget {
  final String userId;

  const UsageHistoryPage({super.key, required this.userId});

  @override
  State<UsageHistoryPage> createState() => _UsageHistoryPageState();
}

class _UsageHistoryPageState extends State<UsageHistoryPage>
    with TickerProviderStateMixin {
  String _selectedUsageType = 'all';
  String _selectedMetric = 'calls'; // 'calls' or 'tokens'
  bool _isDaily = true;
  DateTimeRange? _selectedDateRange;
  List<UsageChartData> _data = [];
  Map<String, dynamic> _summaryStats = {};
  bool _isLoading = false;
  String? _errorMessage;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeDateRange();
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeDateRange() {
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: now.subtract(const Duration(days: 30)),
      end: now,
    );
  }

  Future<void> _loadData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      final data = await fetchUsageData(
        widget.userId,
        isDaily: _isDaily,
        usageType: _selectedUsageType,
        metric: _selectedMetric,
        dateRange: _selectedDateRange,
      );

      final stats = await _calculateSummaryStats();

      if (mounted) {
        setState(() {
          _data = data;
          _summaryStats = stats;
          _isLoading = false;
        });
        _animationController.forward();
      }
    } catch (error, stacktrace) {
      logger.e('Error loading usage data: $error');
      logger.e('Stacktrace: $stacktrace');

      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load usage data. Please try again.';
          _isLoading = false;
        });
      }
    }
  }

  Future<List<UsageChartData>> fetchUsageData(
    String userId, {
    required bool isDaily,
    String usageType = 'all',
    String metric = 'calls',
    DateTimeRange? dateRange,
    int period = 90,
  }) async {
    try {
      DateTime now = DateTime.now().toUtc();
      DateTime startDate =
          dateRange?.start.toUtc() ??
          (isDaily
              ? now.subtract(Duration(days: period))
              : now.subtract(Duration(days: period * 30)));
      DateTime endDate = dateRange?.end.toUtc() ?? now;

      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection('apiUsageRecord')
          .doc(userId)
          .collection('dailyRecords')
          .where('userId', isEqualTo: userId)
          .where('lastUpdateDate', isGreaterThanOrEqualTo: startDate)
          .where('lastUpdateDate', isLessThanOrEqualTo: endDate)
          .orderBy('lastUpdateDate', descending: false)
          .get();

      return querySnapshot.docs.map((doc) {
        ApiUsageRecord record = ApiUsageRecord.fromMap(
          doc.data() as Map<String, dynamic>,
        );

        int value;
        if (metric == 'tokens') {
          value = isDaily
              ? _getFilteredTokenCount(record.dailyTokenCount, usageType)
              : _getFilteredTokenCount(record.monthlyTokenCount, usageType);
        } else {
          value = isDaily
              ? _getFilteredApiCallCount(record.dailyApiCallCount, usageType)
              : _getFilteredApiCallCount(record.monthlyApiCallCount, usageType);
        }

        return UsageChartData(date: record.lastUpdateDate, value: value);
      }).toList();
    } catch (error, stacktrace) {
      logger.e('Error fetching usage data: $error');
      logger.e('Stacktrace: $stacktrace');
      throw Exception('Failed to fetch usage data');
    }
  }

  int _getFilteredApiCallCount(Map<String, int> counts, String usageType) {
    if (usageType == 'all') {
      return counts.values.fold<int>(0, (total, apiCount) => total + apiCount);
    }
    return counts[usageType] ?? 0;
  }

  int _getFilteredTokenCount(Map<String, int> counts, String usageType) {
    if (usageType == 'all') {
      return counts.values.fold<int>(
        0,
        (total, tokenCount) => total + tokenCount,
      );
    }
    return counts[usageType] ?? 0;
  }

  Future<Map<String, dynamic>> _calculateSummaryStats() async {
    try {
      // Calculate stats for different time periods
      final today = DateTime.now().toUtc();
      final yesterday = today.subtract(const Duration(days: 1));
      final thisWeek = today.subtract(const Duration(days: 7));
      final thisMonth = today.subtract(const Duration(days: 30));

      // Get data for different periods
      final todayData = await fetchUsageData(
        widget.userId,
        isDaily: true,
        dateRange: DateTimeRange(start: yesterday, end: today),
      );

      final weekData = await fetchUsageData(
        widget.userId,
        isDaily: true,
        dateRange: DateTimeRange(start: thisWeek, end: today),
      );

      final monthData = await fetchUsageData(
        widget.userId,
        isDaily: true,
        dateRange: DateTimeRange(start: thisMonth, end: today),
      );

      return {
        'todayTotal': todayData.fold<int>(
          0,
          (total, data) => total + data.value,
        ),
        'weekTotal': weekData.fold<int>(0, (total, data) => total + data.value),
        'monthTotal': monthData.fold<int>(
          0,
          (total, data) => total + data.value,
        ),
        'averageDaily': weekData.isNotEmpty
            ? (weekData.fold<int>(0, (total, data) => total + data.value) / 7)
                  .round()
            : 0,
      };
    } catch (error) {
      logger.e('Error calculating summary stats: $error');
      return {
        'todayTotal': 0,
        'weekTotal': 0,
        'monthTotal': 0,
        'averageDaily': 0,
      };
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: EnhancedAppBar(
        title: 'Usage Analytics',
        gradient: AppTheme.primaryGradient,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'Refresh Data',
          ),
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _showDateRangePicker,
            tooltip: 'Select Date Range',
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingState()
          : _errorMessage != null
          ? _buildErrorState()
          : FadeTransition(
              opacity: _fadeAnimation,
              child: RefreshIndicator(
                onRefresh: _loadData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSummaryCards(theme, colorScheme),
                      const SizedBox(height: 24),
                      _buildFiltersSection(theme, colorScheme),
                      const SizedBox(height: 24),
                      _buildChartSection(theme, colorScheme),
                      const SizedBox(height: 24),
                      _buildUsageBreakdown(theme, colorScheme),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Future<void> _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppTheme.primaryGreen),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDateRange) {
      setState(() {
        _selectedDateRange = picked;
      });
      await _loadData();
    }
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading usage data...', style: TextStyle(fontSize: 16)),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage ?? 'An error occurred',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          EnhancedButton(
            text: 'Retry',
            onPressed: _loadData,
            icon: Icons.refresh,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Usage Overview',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'Today',
              '${_summaryStats['todayTotal'] ?? 0}',
              Icons.today,
              AppTheme.accentBlue,
              theme,
              colorScheme,
            ),
            _buildStatCard(
              'This Week',
              '${_summaryStats['weekTotal'] ?? 0}',
              Icons.date_range,
              AppTheme.primaryGreen,
              theme,
              colorScheme,
            ),
            _buildStatCard(
              'This Month',
              '${_summaryStats['monthTotal'] ?? 0}',
              Icons.calendar_month,
              AppTheme.accentCoral,
              theme,
              colorScheme,
            ),
            _buildStatCard(
              'Daily Avg',
              '${_summaryStats['averageDaily'] ?? 0}',
              Icons.trending_up,
              AppTheme.accentPink,
              theme,
              colorScheme,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return EnhancedCard(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
      ),
      border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(ThemeData theme, ColorScheme colorScheme) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Usage Type',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: colorScheme.outline),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedUsageType,
                          isExpanded: true,
                          items:
                              [
                                'all',
                                'chat',
                                'image',
                                'translate',
                                'summarize',
                                'program',
                              ].map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(
                                    value == 'all'
                                        ? 'All Types'
                                        : value.toUpperCase(),
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                );
                              }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedUsageType = value!;
                            });
                            _loadData();
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Metric',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: colorScheme.outline),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedMetric,
                          isExpanded: true,
                          items: [
                            const DropdownMenuItem<String>(
                              value: 'calls',
                              child: Text('API Calls'),
                            ),
                            const DropdownMenuItem<String>(
                              value: 'tokens',
                              child: Text('Tokens'),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedMetric = value!;
                            });
                            _loadData();
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChartSection(ThemeData theme, ColorScheme colorScheme) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Usage Trend',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              Row(
                children: [
                  Text(
                    _isDaily ? 'Daily' : 'Monthly',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Switch(
                    value: _isDaily,
                    onChanged: (value) {
                      setState(() {
                        _isDaily = value;
                      });
                      _loadData();
                    },
                    activeColor: AppTheme.primaryGreen,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_selectedDateRange != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.date_range,
                    size: 16,
                    color: AppTheme.primaryGreen,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.start)} - ${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.end)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.primaryGreen,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 16),
          SizedBox(
            height: 300,
            child: _data.isEmpty
                ? _buildEmptyChartState(theme, colorScheme)
                : UsageChart(data: _data),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyChartState(ThemeData theme, ColorScheme colorScheme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 64,
            color: colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No usage data available',
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your filters or date range',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageBreakdown(ThemeData theme, ColorScheme colorScheme) {
    // TODO: Implement detailed usage breakdown with list of recent activities
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Activity',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: colorScheme.primary),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Detailed activity breakdown will be available soon. This will show individual API calls, timestamps, and usage patterns.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
